#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票数据爬虫配置文件
"""

# 爬虫基础配置
SPIDER_CONFIG = {
    'max_workers': 10,          # 最大线程数
    'timeout': 30,              # 请求超时时间(秒)
    'max_retries': 3,           # 最大重试次数
    'request_delay': 0.5,       # 请求间隔延迟(秒)
}

# 常用股票列表配置
STOCK_LISTS = {
    # 银行股
    'banks': [
        {'code': '600000', 'name': '浦发银行', 'market': 'SH'},
        {'code': '000001', 'name': '平安银行', 'market': 'SZ'},
        {'code': '600036', 'name': '招商银行', 'market': 'SH'},
        {'code': '601166', 'name': '兴业银行', 'market': 'SH'},
        {'code': '000002', 'name': '万科A', 'market': 'SZ'},
    ],
    
    # 白酒股
    'liquor': [
        {'code': '000858', 'name': '五粮液', 'market': 'SZ'},
        {'code': '600519', 'name': '贵州茅台', 'market': 'SH'},
        {'code': '000568', 'name': '泸州老窖', 'market': 'SZ'},
        {'code': '002304', 'name': '洋河股份', 'market': 'SZ'},
    ],
    
    # 科技股
    'tech': [
        {'code': '000063', 'name': '中兴通讯', 'market': 'SZ'},
        {'code': '002415', 'name': '海康威视', 'market': 'SZ'},
        {'code': '300059', 'name': '东方财富', 'market': 'SZ'},
        {'code': '300750', 'name': '宁德时代', 'market': 'SZ'},
    ],
    
    # 自定义股票列表
    'custom': [
        {'code': '600000', 'name': '浦发银行', 'market': 'SH'},
        {'code': '000001', 'name': '平安银行', 'market': 'SZ'},
    ]
}

# 数据获取配置
DATA_CONFIG = {
    # 默认时间范围
    'default_start_date': '20240101',
    'default_end_date': '20241231',
    
    # 分钟数据默认天数
    'minute_data_days': 7,
    
    # 滚动获取配置
    'rolling_batch_days': 7,    # 每批获取天数
    'rolling_delay': 0.5,       # 批次间延迟
    
    # 默认复权类型
    'default_adjust_type': 'FORWARD',  # NONE, FORWARD, BACKWARD
}

# 文件保存配置
FILE_CONFIG = {
    'save_directory': './data',     # 数据保存目录
    'csv_encoding': 'utf-8-sig',    # CSV文件编码
    'filename_format': '{stock_code}_{data_type}_{timestamp}',  # 文件名格式
}

# 市场时间配置
MARKET_CONFIG = {
    'trading_hours': {
        'morning': ('09:30', '11:30'),
        'afternoon': ('13:00', '15:00'),
    },
    'trading_days': [0, 1, 2, 3, 4],  # 周一到周五 (0=周一)
}

# 日志配置
LOG_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(threadName)s - %(levelname)s - %(message)s',
    'file': 'spider.log',
    'max_size': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5,
}

# API配置
API_CONFIG = {
    'base_url': 'https://push2his.eastmoney.com/api/qt/stock/kline/get',
    'headers': {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Referer': 'http://quote.eastmoney.com',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive'
    },
    'fields1': 'f1',
    'fields2': 'f51,f52,f53,f54,f55,f56',
}

# 实时监控配置
MONITOR_CONFIG = {
    'interval': 60,             # 监控间隔(秒)
    'enable_trading_hours_check': True,  # 是否检查交易时间
    'alert_threshold': {
        'price_change': 5.0,    # 价格变动阈值(%)
        'volume_ratio': 2.0,    # 成交量倍数阈值
    }
}

# 数据验证配置
VALIDATION_CONFIG = {
    'price_range': (0.01, 10000),      # 价格合理范围
    'volume_range': (0, 1000000000),   # 成交量合理范围
    'required_fields': ['datetime', 'open', 'high', 'low', 'close', 'volume'],
}

def get_stock_list(list_name='custom'):
    """
    获取指定的股票列表
    
    Args:
        list_name: 股票列表名称
        
    Returns:
        股票列表
    """
    return STOCK_LISTS.get(list_name, STOCK_LISTS['custom'])

def get_stock_codes(list_name='custom'):
    """
    获取指定股票列表的代码
    
    Args:
        list_name: 股票列表名称
        
    Returns:
        股票代码列表
    """
    stocks = get_stock_list(list_name)
    return [stock['code'] for stock in stocks]

def get_config(config_name):
    """
    获取指定配置
    
    Args:
        config_name: 配置名称
        
    Returns:
        配置字典
    """
    config_map = {
        'spider': SPIDER_CONFIG,
        'data': DATA_CONFIG,
        'file': FILE_CONFIG,
        'market': MARKET_CONFIG,
        'log': LOG_CONFIG,
        'api': API_CONFIG,
        'monitor': MONITOR_CONFIG,
        'validation': VALIDATION_CONFIG,
    }
    return config_map.get(config_name, {})

# 示例使用
if __name__ == "__main__":
    print("股票数据爬虫配置")
    print("=" * 40)
    
    print("\n可用股票列表:")
    for list_name, stocks in STOCK_LISTS.items():
        print(f"  {list_name}: {len(stocks)} 只股票")
        for stock in stocks[:3]:  # 只显示前3只
            print(f"    {stock['code']} - {stock['name']}")
        if len(stocks) > 3:
            print(f"    ... 还有 {len(stocks) - 3} 只股票")
    
    print(f"\n爬虫配置:")
    for key, value in SPIDER_CONFIG.items():
        print(f"  {key}: {value}")
    
    print(f"\n数据配置:")
    for key, value in DATA_CONFIG.items():
        print(f"  {key}: {value}")
