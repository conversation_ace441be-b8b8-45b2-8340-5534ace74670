#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票数据爬取模块 - 基于东方财富接口
支持多线程并发爬取日线和分时数据
"""

import requests
import pandas as pd
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime, timedelta
import json
import logging
from typing import List, Dict, Optional, Tuple
from queue import Queue
import random
from dataclasses import dataclass
from enum import Enum

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(threadName)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class KLineType(Enum):
    """K线类型枚举"""
    MIN_1 = 1      # 1分钟
    MIN_5 = 5      # 5分钟
    MIN_15 = 15    # 15分钟
    MIN_30 = 30    # 30分钟
    MIN_60 = 60    # 60分钟
    DAILY = 101    # 日线
    WEEKLY = 102   # 周线
    MONTHLY = 103  # 月线


class AdjustType(Enum):
    """复权类型枚举"""
    NONE = 0       # 不复权
    FORWARD = 1    # 前复权
    BACKWARD = 2   # 后复权


@dataclass
class StockConfig:
    """股票配置"""
    code: str
    name: str = ""
    market: str = ""  # 'SH' or 'SZ' or 'BJ'


class StockDataSpider:
    """股票数据爬虫类"""

    def __init__(self, max_workers: int = 10, timeout: int = 30, max_retries: int = 3):
        """
        初始化爬虫

        Args:
            max_workers: 最大线程数
            timeout: 请求超时时间(秒)
            max_retries: 最大重试次数
        """
        self.max_workers = max_workers
        self.timeout = timeout
        self.max_retries = max_retries
        self.session = requests.Session()

        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'http://quote.eastmoney.com',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive'
        })

        # 数据存储
        self.data_cache = {}
        self.lock = threading.Lock()

    def _get_secid(self, stock_code: str) -> str:
        """
        根据股票代码生成secid

        Args:
            stock_code: 股票代码，如 '600000', '000001'

        Returns:
            secid: 如 '1.600000', '0.000001'
        """
        if stock_code.startswith('6'):
            # 沪市
            return f"1.{stock_code}"
        elif stock_code.startswith(('000', '002', '003', '300', '301')):
            # 深市
            return f"0.{stock_code}"
        elif stock_code.startswith(('8', '4')):
            # 北交所
            return f"0.{stock_code}"
        else:
            # 默认深市
            return f"0.{stock_code}"

    def _build_url(self, stock_code: str, klt: int, fqt: int = 1,
                   beg: str = "19900101", end: str = "20500101",
                   lmt: int = 3000) -> str:
        """
        构建请求URL

        Args:
            stock_code: 股票代码
            klt: K线类型
            fqt: 复权类型
            beg: 开始日期
            end: 结束日期
            lmt: 返回条数限制

        Returns:
            完整的请求URL
        """
        secid = self._get_secid(stock_code)
        base_url = "https://push2his.eastmoney.com/api/qt/stock/kline/get"

        params = {
            'secid': secid,
            'klt': klt,
            'fqt': fqt,
            'beg': beg,
            'end': end,
            'lmt': lmt,
            'fields1': 'f1',
            'fields2': 'f51,f52,f53,f54,f55,f56'
        }

        param_str = '&'.join([f"{k}={v}" for k, v in params.items()])
        return f"{base_url}?{param_str}"

    def _request_with_retry(self, url: str) -> Optional[Dict]:
        """
        带重试机制的请求

        Args:
            url: 请求URL

        Returns:
            响应数据或None
        """
        for attempt in range(self.max_retries):
            try:
                # 添加随机延迟避免频率限制
                if attempt > 0:
                    delay = random.uniform(1, 3) * (2 ** attempt)
                    time.sleep(delay)
                    logger.info(f"重试第{attempt}次，延迟{delay:.2f}秒")

                response = self.session.get(url, timeout=self.timeout)
                response.raise_for_status()

                data = response.json()
                if data.get('rc') == 0 and data.get('data'):
                    return data
                else:
                    logger.warning(f"API返回错误: {data.get('msg', 'Unknown error')}")
                    continue

            except requests.exceptions.Timeout:
                logger.warning(f"请求超时 (尝试 {attempt + 1}/{self.max_retries}): {url}")
            except requests.exceptions.RequestException as e:
                logger.warning(f"请求异常 (尝试 {attempt + 1}/{self.max_retries}): {e}")
            except json.JSONDecodeError as e:
                logger.warning(f"JSON解析错误 (尝试 {attempt + 1}/{self.max_retries}): {e}")
            except Exception as e:
                logger.error(f"未知错误 (尝试 {attempt + 1}/{self.max_retries}): {e}")

        logger.error(f"请求失败，已达到最大重试次数: {url}")
        return None

    def _parse_kline_data(self, data: Dict, stock_code: str) -> pd.DataFrame:
        """
        解析K线数据

        Args:
            data: API返回的数据
            stock_code: 股票代码

        Returns:
            解析后的DataFrame
        """
        if not data or not data.get('data') or not data['data'].get('klines'):
            logger.warning(f"股票 {stock_code} 无K线数据")
            return pd.DataFrame()

        klines = data['data']['klines']
        rows = []

        for line in klines:
            # 数据格式: 日期,开盘,收盘,最高,最低,成交量,成交额,振幅,涨跌幅,涨跌额,换手率
            parts = line.split(',')
            if len(parts) >= 6:
                try:
                    row = {
                        'stock_code': stock_code,
                        'datetime': parts[0],
                        'open': float(parts[1]),
                        'close': float(parts[2]),
                        'high': float(parts[3]),
                        'low': float(parts[4]),
                        'volume': int(parts[5]),
                        'amount': float(parts[6]) if len(parts) > 6 else 0,
                        'amplitude': float(parts[7]) if len(parts) > 7 else 0,
                        'pct_change': float(parts[8]) if len(parts) > 8 else 0,
                        'change': float(parts[9]) if len(parts) > 9 else 0,
                        'turnover': float(parts[10]) if len(parts) > 10 else 0
                    }
                    rows.append(row)
                except (ValueError, IndexError) as e:
                    logger.warning(f"解析数据行失败: {line}, 错误: {e}")
                    continue

        if not rows:
            logger.warning(f"股票 {stock_code} 没有有效的K线数据")
            return pd.DataFrame()

        df = pd.DataFrame(rows)

        # 转换日期时间格式
        try:
            df['datetime'] = pd.to_datetime(df['datetime'])
        except Exception as e:
            logger.warning(f"日期时间转换失败: {e}")

        # 按时间排序
        df = df.sort_values('datetime').reset_index(drop=True)

        logger.info(f"股票 {stock_code} 解析完成，共 {len(df)} 条数据")
        return df

    def fetch_single_stock_data(self, stock_code: str, kline_type: KLineType,
                               adjust_type: AdjustType = AdjustType.FORWARD,
                               start_date: str = "19900101",
                               end_date: str = "20500101",
                               limit: int = 3000) -> pd.DataFrame:
        """
        获取单只股票数据

        Args:
            stock_code: 股票代码
            kline_type: K线类型
            adjust_type: 复权类型
            start_date: 开始日期 (YYYYMMDD)
            end_date: 结束日期 (YYYYMMDD)
            limit: 返回条数限制

        Returns:
            股票数据DataFrame
        """
        logger.info(f"开始获取股票 {stock_code} 的 {kline_type.name} 数据")

        url = self._build_url(
            stock_code=stock_code,
            klt=kline_type.value,
            fqt=adjust_type.value,
            beg=start_date,
            end=end_date,
            lmt=limit
        )

        data = self._request_with_retry(url)
        if data is None:
            logger.error(f"获取股票 {stock_code} 数据失败")
            return pd.DataFrame()

        df = self._parse_kline_data(data, stock_code)

        # 缓存数据
        cache_key = f"{stock_code}_{kline_type.name}_{adjust_type.name}"
        with self.lock:
            self.data_cache[cache_key] = df

        return df

    def fetch_multiple_stocks_data(self, stock_codes: List[str],
                                  kline_type: KLineType,
                                  adjust_type: AdjustType = AdjustType.FORWARD,
                                  start_date: str = "19900101",
                                  end_date: str = "20500101") -> Dict[str, pd.DataFrame]:
        """
        并发获取多只股票数据

        Args:
            stock_codes: 股票代码列表
            kline_type: K线类型
            adjust_type: 复权类型
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            股票代码到DataFrame的字典
        """
        logger.info(f"开始并发获取 {len(stock_codes)} 只股票的 {kline_type.name} 数据")

        results = {}

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交任务
            future_to_stock = {
                executor.submit(
                    self.fetch_single_stock_data,
                    stock_code, kline_type, adjust_type, start_date, end_date
                ): stock_code for stock_code in stock_codes
            }

            # 收集结果
            for future in as_completed(future_to_stock):
                stock_code = future_to_stock[future]
                try:
                    df = future.result()
                    results[stock_code] = df
                    logger.info(f"股票 {stock_code} 数据获取完成")
                except Exception as e:
                    logger.error(f"股票 {stock_code} 数据获取异常: {e}")
                    results[stock_code] = pd.DataFrame()

        logger.info(f"所有股票数据获取完成，成功: {sum(1 for df in results.values() if not df.empty)}/{len(stock_codes)}")
        return results

    def fetch_incremental_data(self, stock_code: str, kline_type: KLineType,
                              last_datetime: str, adjust_type: AdjustType = AdjustType.FORWARD) -> pd.DataFrame:
        """
        获取增量数据（用于实时更新）

        Args:
            stock_code: 股票代码
            kline_type: K线类型
            last_datetime: 最后一条数据的时间 (YYYYMMDD 或 YYYYMMDD HHMMSS)
            adjust_type: 复权类型

        Returns:
            增量数据DataFrame
        """
        # 将时间格式转换为API需要的格式
        if len(last_datetime) > 8:
            # 包含时分秒，提取日期部分
            start_date = last_datetime[:8]
        else:
            start_date = last_datetime

        # 结束日期设为今天
        end_date = datetime.now().strftime("%Y%m%d")

        logger.info(f"获取股票 {stock_code} 从 {start_date} 到 {end_date} 的增量数据")

        return self.fetch_single_stock_data(
            stock_code=stock_code,
            kline_type=kline_type,
            adjust_type=adjust_type,
            start_date=start_date,
            end_date=end_date
        )

    def rolling_fetch_minute_data(self, stock_codes: List[str],
                                 days_back: int = 30,
                                 kline_type: KLineType = KLineType.MIN_1) -> Dict[str, pd.DataFrame]:
        """
        滚动获取分钟数据（解决3000条限制）

        Args:
            stock_codes: 股票代码列表
            days_back: 向前获取多少天的数据
            kline_type: 分钟K线类型

        Returns:
            完整的分钟数据
        """
        logger.info(f"开始滚动获取 {len(stock_codes)} 只股票最近 {days_back} 天的分钟数据")

        # 计算日期范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days_back)

        all_results = {}

        for stock_code in stock_codes:
            logger.info(f"处理股票 {stock_code}")
            all_data = []
            current_date = start_date

            while current_date <= end_date:
                # 每次获取7天的数据，避免超过3000条限制
                batch_end = min(current_date + timedelta(days=7), end_date)

                start_str = current_date.strftime("%Y%m%d")
                end_str = batch_end.strftime("%Y%m%d")

                logger.info(f"获取 {stock_code} 从 {start_str} 到 {end_str} 的数据")

                df = self.fetch_single_stock_data(
                    stock_code=stock_code,
                    kline_type=kline_type,
                    start_date=start_str,
                    end_date=end_str,
                    limit=3000
                )

                if not df.empty:
                    all_data.append(df)

                current_date = batch_end + timedelta(days=1)

                # 添加延迟避免频率限制
                time.sleep(0.5)

            # 合并所有数据
            if all_data:
                combined_df = pd.concat(all_data, ignore_index=True)
                # 去重并排序
                combined_df = combined_df.drop_duplicates(subset=['datetime']).sort_values('datetime').reset_index(drop=True)
                all_results[stock_code] = combined_df
                logger.info(f"股票 {stock_code} 共获取 {len(combined_df)} 条分钟数据")
            else:
                all_results[stock_code] = pd.DataFrame()
                logger.warning(f"股票 {stock_code} 未获取到数据")

        return all_results

    def simulate_realtime_playback(self, stock_code: str,
                                  start_date: str, end_date: str,
                                  kline_type: KLineType = KLineType.MIN_1,
                                  speed_multiplier: float = 1.0,
                                  callback=None) -> None:
        """
        模拟实时回放

        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            kline_type: K线类型
            speed_multiplier: 播放速度倍数
            callback: 数据回调函数
        """
        logger.info(f"开始模拟股票 {stock_code} 的实时回放")

        # 获取历史数据
        df = self.fetch_single_stock_data(
            stock_code=stock_code,
            kline_type=kline_type,
            start_date=start_date,
            end_date=end_date
        )

        if df.empty:
            logger.error(f"股票 {stock_code} 无历史数据，无法进行回放")
            return

        logger.info(f"开始回放 {len(df)} 条数据，速度倍数: {speed_multiplier}")

        # 计算时间间隔
        if kline_type == KLineType.MIN_1:
            interval = 60 / speed_multiplier  # 1分钟数据
        elif kline_type == KLineType.MIN_5:
            interval = 300 / speed_multiplier  # 5分钟数据
        else:
            interval = 1 / speed_multiplier  # 其他类型默认1秒

        for index, row in df.iterrows():
            if callback:
                callback(row.to_dict())
            else:
                logger.info(f"回放数据: {row['datetime']} - 开:{row['open']} 高:{row['high']} 低:{row['low']} 收:{row['close']} 量:{row['volume']}")

            time.sleep(interval)

        logger.info("实时回放完成")

    def get_cached_data(self, stock_code: str, kline_type: KLineType,
                       adjust_type: AdjustType = AdjustType.FORWARD) -> Optional[pd.DataFrame]:
        """
        获取缓存的数据

        Args:
            stock_code: 股票代码
            kline_type: K线类型
            adjust_type: 复权类型

        Returns:
            缓存的DataFrame或None
        """
        cache_key = f"{stock_code}_{kline_type.name}_{adjust_type.name}"
        with self.lock:
            return self.data_cache.get(cache_key)

    def clear_cache(self):
        """清空缓存"""
        with self.lock:
            self.data_cache.clear()
        logger.info("缓存已清空")

    def save_to_csv(self, data: Dict[str, pd.DataFrame], base_filename: str = "stock_data"):
        """
        保存数据到CSV文件

        Args:
            data: 股票数据字典
            base_filename: 基础文件名
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        for stock_code, df in data.items():
            if not df.empty:
                filename = f"{base_filename}_{stock_code}_{timestamp}.csv"
                df.to_csv(filename, index=False, encoding='utf-8-sig')
                logger.info(f"股票 {stock_code} 数据已保存到 {filename}")

    def get_market_status(self) -> Dict[str, str]:
        """
        获取市场状态信息

        Returns:
            市场状态字典
        """
        now = datetime.now()
        current_time = now.strftime("%H:%M")

        # 简单的市场时间判断
        if "09:30" <= current_time <= "11:30" or "13:00" <= current_time <= "15:00":
            if now.weekday() < 5:  # 周一到周五
                status = "交易中"
            else:
                status = "休市"
        else:
            status = "休市"

        return {
            "current_time": now.strftime("%Y-%m-%d %H:%M:%S"),
            "market_status": status,
            "is_trading": status == "交易中"
        }


def demo_usage():
    """演示如何使用股票数据爬虫"""

    # 创建爬虫实例
    spider = StockDataSpider(max_workers=5, timeout=30, max_retries=3)

    # 定义要爬取的股票
    stock_codes = ['600000', '000001', '000002', '600036', '000858']  # 浦发银行、平安银行、万科A、招商银行、五粮液

    print("=== 股票数据爬虫演示 ===")

    # 1. 获取日线数据
    print("\n1. 获取多只股票的日线数据...")
    daily_data = spider.fetch_multiple_stocks_data(
        stock_codes=stock_codes,
        kline_type=KLineType.DAILY,
        adjust_type=AdjustType.FORWARD,
        start_date="20240101",
        end_date="20241231"
    )

    for code, df in daily_data.items():
        if not df.empty:
            print(f"股票 {code}: {len(df)} 条日线数据")
            print(f"  时间范围: {df['datetime'].min()} 到 {df['datetime'].max()}")
            print(f"  最新价格: {df.iloc[-1]['close']:.2f}")
        else:
            print(f"股票 {code}: 无数据")

    # 2. 获取分钟数据（最近7天）
    print("\n2. 获取分钟数据（最近7天）...")
    minute_data = spider.rolling_fetch_minute_data(
        stock_codes=stock_codes[:2],  # 只取前两只股票演示
        days_back=7,
        kline_type=KLineType.MIN_5
    )

    for code, df in minute_data.items():
        if not df.empty:
            print(f"股票 {code}: {len(df)} 条5分钟数据")
        else:
            print(f"股票 {code}: 无分钟数据")

    # 3. 保存数据到CSV
    print("\n3. 保存数据到CSV文件...")
    spider.save_to_csv(daily_data, "daily_data")
    spider.save_to_csv(minute_data, "minute_data")

    # 4. 获取市场状态
    print("\n4. 市场状态信息...")
    market_info = spider.get_market_status()
    for key, value in market_info.items():
        print(f"  {key}: {value}")

    # 5. 演示实时回放（仅演示几条数据）
    print("\n5. 演示实时回放（前10条数据）...")
    if not daily_data['600000'].empty:
        sample_data = daily_data['600000'].head(10)

        def playback_callback(data_point):
            print(f"  回放: {data_point['datetime']} - 收盘价: {data_point['close']:.2f}")

        # 模拟快速回放
        for _, row in sample_data.iterrows():
            playback_callback(row.to_dict())
            time.sleep(0.1)  # 快速演示

    print("\n=== 演示完成 ===")


class StockDataManager:
    """股票数据管理器 - 高级封装"""

    def __init__(self, spider: StockDataSpider):
        self.spider = spider
        self.stock_configs = {}

    def add_stock(self, code: str, name: str = "", market: str = ""):
        """添加股票配置"""
        self.stock_configs[code] = StockConfig(code=code, name=name, market=market)
        logger.info(f"添加股票: {code} {name}")

    def remove_stock(self, code: str):
        """移除股票配置"""
        if code in self.stock_configs:
            del self.stock_configs[code]
            logger.info(f"移除股票: {code}")

    def get_all_daily_data(self, start_date: str = "20240101",
                          end_date: str = None) -> Dict[str, pd.DataFrame]:
        """获取所有配置股票的日线数据"""
        if not end_date:
            end_date = datetime.now().strftime("%Y%m%d")

        stock_codes = list(self.stock_configs.keys())
        return self.spider.fetch_multiple_stocks_data(
            stock_codes=stock_codes,
            kline_type=KLineType.DAILY,
            start_date=start_date,
            end_date=end_date
        )

    def get_all_minute_data(self, days_back: int = 7) -> Dict[str, pd.DataFrame]:
        """获取所有配置股票的分钟数据"""
        stock_codes = list(self.stock_configs.keys())
        return self.spider.rolling_fetch_minute_data(
            stock_codes=stock_codes,
            days_back=days_back
        )

    def start_realtime_monitor(self, interval_seconds: int = 60):
        """启动实时监控（简单版本）"""
        logger.info("启动实时监控...")

        while True:
            try:
                market_info = self.spider.get_market_status()
                if market_info['is_trading']:
                    logger.info("市场交易中，获取最新数据...")

                    # 获取最新的分钟数据
                    latest_data = self.get_all_minute_data(days_back=1)

                    for code, df in latest_data.items():
                        if not df.empty:
                            latest_row = df.iloc[-1]
                            logger.info(f"{code}: {latest_row['datetime']} - 价格: {latest_row['close']:.2f}")
                else:
                    logger.info("市场休市中...")

                time.sleep(interval_seconds)

            except KeyboardInterrupt:
                logger.info("停止实时监控")
                break
            except Exception as e:
                logger.error(f"实时监控异常: {e}")
                time.sleep(interval_seconds)


if __name__ == "__main__":
    # 运行演示
    demo_usage()

    # 如果需要实时监控，取消下面的注释
    # spider = StockDataSpider()
    # manager = StockDataManager(spider)
    # manager.add_stock('600000', '浦发银行', 'SH')
    # manager.add_stock('000001', '平安银行', 'SZ')
    # manager.start_realtime_monitor()