#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票数据爬虫命令行工具
"""

import argparse
import sys
from datetime import datetime, timedelta
from sprider import StockDataSpider, StockDataManager, KLineType, AdjustType
from config import get_stock_codes, get_config, STOCK_LISTS

def parse_kline_type(kline_str):
    """解析K线类型"""
    kline_map = {
        '1m': KLineType.MIN_1,
        '5m': KLineType.MIN_5,
        '15m': KLineType.MIN_15,
        '30m': KLineType.MIN_30,
        '60m': KLineType.MIN_60,
        '1h': KLineType.MIN_60,
        'daily': KLineType.DAILY,
        'd': KLineType.DAILY,
        'weekly': KLineType.WEEKLY,
        'w': KLineType.WEEKLY,
        'monthly': KLineType.MONTHLY,
        'm': KLineType.MONTHLY,
    }
    return kline_map.get(kline_str.lower(), KLineType.DAILY)

def parse_adjust_type(adjust_str):
    """解析复权类型"""
    adjust_map = {
        'none': AdjustType.NONE,
        'forward': AdjustType.FORWARD,
        'backward': AdjustType.BACKWARD,
        '0': AdjustType.NONE,
        '1': AdjustType.FORWARD,
        '2': AdjustType.BACKWARD,
    }
    return adjust_map.get(adjust_str.lower(), AdjustType.FORWARD)

def cmd_fetch_single(args):
    """获取单只股票数据"""
    print(f"获取股票 {args.code} 的数据...")
    
    spider = StockDataSpider(
        max_workers=args.workers,
        timeout=args.timeout,
        max_retries=args.retries
    )
    
    kline_type = parse_kline_type(args.kline)
    adjust_type = parse_adjust_type(args.adjust)
    
    data = spider.fetch_single_stock_data(
        stock_code=args.code,
        kline_type=kline_type,
        adjust_type=adjust_type,
        start_date=args.start,
        end_date=args.end
    )
    
    if not data.empty:
        print(f"成功获取 {len(data)} 条数据")
        print(f"时间范围: {data['datetime'].min()} 到 {data['datetime'].max()}")
        
        if args.output:
            filename = args.output
        else:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{args.code}_{args.kline}_{timestamp}.csv"
        
        data.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"数据已保存到: {filename}")
        
        if args.show:
            print("\n最近5条数据:")
            print(data.tail()[['datetime', 'open', 'high', 'low', 'close', 'volume']])
    else:
        print("未获取到数据")

def cmd_fetch_multiple(args):
    """获取多只股票数据"""
    if args.list:
        stock_codes = get_stock_codes(args.list)
        print(f"使用预设股票列表: {args.list} ({len(stock_codes)} 只股票)")
    else:
        stock_codes = args.codes.split(',')
        print(f"获取指定股票: {stock_codes}")
    
    spider = StockDataSpider(
        max_workers=args.workers,
        timeout=args.timeout,
        max_retries=args.retries
    )
    
    kline_type = parse_kline_type(args.kline)
    adjust_type = parse_adjust_type(args.adjust)
    
    results = spider.fetch_multiple_stocks_data(
        stock_codes=stock_codes,
        kline_type=kline_type,
        adjust_type=adjust_type,
        start_date=args.start,
        end_date=args.end
    )
    
    success_count = 0
    for code, data in results.items():
        if not data.empty:
            success_count += 1
            print(f"股票 {code}: {len(data)} 条数据")
            
            if args.output:
                filename = f"{args.output}_{code}.csv"
            else:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"{code}_{args.kline}_{timestamp}.csv"
            
            data.to_csv(filename, index=False, encoding='utf-8-sig')
            print(f"  已保存到: {filename}")
        else:
            print(f"股票 {code}: 无数据")
    
    print(f"\n完成! 成功获取 {success_count}/{len(stock_codes)} 只股票的数据")

def cmd_fetch_minute(args):
    """获取分钟数据（滚动获取）"""
    stock_codes = args.codes.split(',') if args.codes else ['600000']
    
    spider = StockDataSpider(
        max_workers=args.workers,
        timeout=args.timeout,
        max_retries=args.retries
    )
    
    kline_type = parse_kline_type(args.kline)
    
    print(f"滚动获取 {len(stock_codes)} 只股票最近 {args.days} 天的分钟数据...")
    
    results = spider.rolling_fetch_minute_data(
        stock_codes=stock_codes,
        days_back=args.days,
        kline_type=kline_type
    )
    
    for code, data in results.items():
        if not data.empty:
            print(f"股票 {code}: {len(data)} 条分钟数据")
            
            if args.output:
                filename = f"{args.output}_{code}.csv"
            else:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"{code}_minute_{args.days}days_{timestamp}.csv"
            
            data.to_csv(filename, index=False, encoding='utf-8-sig')
            print(f"  已保存到: {filename}")
        else:
            print(f"股票 {code}: 无数据")

def cmd_list_stocks(args):
    """列出可用的股票列表"""
    print("可用的股票列表:")
    print("=" * 50)
    
    for list_name, stocks in STOCK_LISTS.items():
        print(f"\n{list_name.upper()} ({len(stocks)} 只股票):")
        for stock in stocks:
            print(f"  {stock['code']} - {stock['name']} ({stock['market']})")

def cmd_monitor(args):
    """实时监控"""
    if args.list:
        stock_codes = get_stock_codes(args.list)
    else:
        stock_codes = args.codes.split(',') if args.codes else ['600000']
    
    spider = StockDataSpider()
    manager = StockDataManager(spider)
    
    # 添加股票
    for code in stock_codes:
        manager.add_stock(code)
    
    print(f"开始监控 {len(stock_codes)} 只股票...")
    print("按 Ctrl+C 停止监控")
    
    try:
        manager.start_realtime_monitor(interval_seconds=args.interval)
    except KeyboardInterrupt:
        print("\n监控已停止")

def main():
    parser = argparse.ArgumentParser(description='股票数据爬虫命令行工具')
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 通用参数
    def add_common_args(p):
        p.add_argument('--workers', type=int, default=5, help='线程数 (默认: 5)')
        p.add_argument('--timeout', type=int, default=30, help='超时时间 (默认: 30秒)')
        p.add_argument('--retries', type=int, default=3, help='重试次数 (默认: 3)')
    
    # 获取单只股票数据
    parser_single = subparsers.add_parser('fetch', help='获取单只股票数据')
    parser_single.add_argument('code', help='股票代码 (如: 600000)')
    parser_single.add_argument('--kline', default='daily', 
                              help='K线类型: 1m,5m,15m,30m,60m,daily,weekly,monthly (默认: daily)')
    parser_single.add_argument('--adjust', default='forward',
                              help='复权类型: none,forward,backward (默认: forward)')
    parser_single.add_argument('--start', default='********', help='开始日期 (默认: ********)')
    parser_single.add_argument('--end', default='********', help='结束日期 (默认: ********)')
    parser_single.add_argument('--output', help='输出文件名')
    parser_single.add_argument('--show', action='store_true', help='显示最近数据')
    add_common_args(parser_single)
    parser_single.set_defaults(func=cmd_fetch_single)
    
    # 获取多只股票数据
    parser_multi = subparsers.add_parser('fetch-multi', help='获取多只股票数据')
    parser_multi.add_argument('--codes', help='股票代码列表，逗号分隔 (如: 600000,000001)')
    parser_multi.add_argument('--list', help='使用预设股票列表: banks,liquor,tech,custom')
    parser_multi.add_argument('--kline', default='daily', help='K线类型 (默认: daily)')
    parser_multi.add_argument('--adjust', default='forward', help='复权类型 (默认: forward)')
    parser_multi.add_argument('--start', default='********', help='开始日期 (默认: ********)')
    parser_multi.add_argument('--end', default='********', help='结束日期 (默认: ********)')
    parser_multi.add_argument('--output', help='输出文件名前缀')
    add_common_args(parser_multi)
    parser_multi.set_defaults(func=cmd_fetch_multiple)
    
    # 获取分钟数据
    parser_minute = subparsers.add_parser('fetch-minute', help='获取分钟数据（滚动获取）')
    parser_minute.add_argument('--codes', help='股票代码列表，逗号分隔 (默认: 600000)')
    parser_minute.add_argument('--kline', default='1m', help='分钟K线类型: 1m,5m,15m,30m,60m (默认: 1m)')
    parser_minute.add_argument('--days', type=int, default=7, help='获取天数 (默认: 7)')
    parser_minute.add_argument('--output', help='输出文件名前缀')
    add_common_args(parser_minute)
    parser_minute.set_defaults(func=cmd_fetch_minute)
    
    # 列出股票
    parser_list = subparsers.add_parser('list', help='列出可用的股票列表')
    parser_list.set_defaults(func=cmd_list_stocks)
    
    # 实时监控
    parser_monitor = subparsers.add_parser('monitor', help='实时监控股票')
    parser_monitor.add_argument('--codes', help='股票代码列表，逗号分隔')
    parser_monitor.add_argument('--list', help='使用预设股票列表')
    parser_monitor.add_argument('--interval', type=int, default=60, help='监控间隔秒数 (默认: 60)')
    parser_monitor.set_defaults(func=cmd_monitor)
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    try:
        args.func(args)
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
