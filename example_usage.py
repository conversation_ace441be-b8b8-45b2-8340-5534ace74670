#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票数据爬虫使用示例
演示如何使用 StockDataSpider 类获取股票数据
"""

from sprider import StockDataSpider, StockDataManager, KLineType, AdjustType
import time
from datetime import datetime, timedel<PERSON>

def basic_usage_example():
    """基础使用示例"""
    print("=== 基础使用示例 ===")
    
    # 创建爬虫实例
    spider = StockDataSpider(max_workers=3, timeout=30, max_retries=3)
    
    # 1. 获取单只股票的日线数据
    print("\n1. 获取浦发银行(600000)的日线数据...")
    daily_data = spider.fetch_single_stock_data(
        stock_code='600000',
        kline_type=KLineType.DAILY,
        adjust_type=AdjustType.FORWARD,
        start_date='20240101',
        end_date='20241231'
    )
    
    if not daily_data.empty:
        print(f"获取到 {len(daily_data)} 条日线数据")
        print("最近5天数据:")
        print(daily_data.tail()[['datetime', 'open', 'high', 'low', 'close', 'volume']])
    
    # 2. 获取分钟数据
    print("\n2. 获取浦发银行(600000)的5分钟数据...")
    minute_data = spider.fetch_single_stock_data(
        stock_code='600000',
        kline_type=KLineType.MIN_5,
        start_date='20250617',
        end_date='20250618'
    )
    
    if not minute_data.empty:
        print(f"获取到 {len(minute_data)} 条5分钟数据")
        print("最近5条数据:")
        print(minute_data.tail()[['datetime', 'open', 'high', 'low', 'close', 'volume']])

def multi_stock_example():
    """多股票并发获取示例"""
    print("\n=== 多股票并发获取示例 ===")
    
    spider = StockDataSpider(max_workers=5)
    
    # 定义股票列表
    stocks = ['600000', '000001', '000002', '600036', '000858']
    stock_names = ['浦发银行', '平安银行', '万科A', '招商银行', '五粮液']
    
    print(f"并发获取 {len(stocks)} 只股票的日线数据...")
    
    # 并发获取日线数据
    results = spider.fetch_multiple_stocks_data(
        stock_codes=stocks,
        kline_type=KLineType.DAILY,
        start_date='20240601',
        end_date='20241231'
    )
    
    # 显示结果
    for i, (code, df) in enumerate(results.items()):
        name = stock_names[i] if i < len(stock_names) else "未知"
        if not df.empty:
            latest = df.iloc[-1]
            print(f"{name}({code}): {len(df)}条数据, 最新价格: {latest['close']:.2f}")
        else:
            print(f"{name}({code}): 无数据")

def rolling_minute_data_example():
    """滚动获取分钟数据示例（解决3000条限制）"""
    print("\n=== 滚动获取分钟数据示例 ===")
    
    spider = StockDataSpider()
    
    # 获取最近30天的1分钟数据
    print("获取浦发银行最近30天的1分钟数据...")
    minute_data = spider.rolling_fetch_minute_data(
        stock_codes=['600000'],
        days_back=30,
        kline_type=KLineType.MIN_1
    )
    
    for code, df in minute_data.items():
        if not df.empty:
            print(f"股票 {code}: 获取到 {len(df)} 条1分钟数据")
            print(f"时间范围: {df['datetime'].min()} 到 {df['datetime'].max()}")
            
            # 保存到CSV
            filename = f"minute_data_{code}_30days.csv"
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            print(f"数据已保存到 {filename}")

def realtime_simulation_example():
    """实时回放模拟示例"""
    print("\n=== 实时回放模拟示例 ===")
    
    spider = StockDataSpider()
    
    # 定义回调函数处理每条数据
    def data_callback(data_point):
        print(f"[回放] {data_point['datetime']} - "
              f"开:{data_point['open']:.2f} "
              f"高:{data_point['high']:.2f} "
              f"低:{data_point['low']:.2f} "
              f"收:{data_point['close']:.2f} "
              f"量:{data_point['volume']}")
    
    print("开始模拟实时回放（仅前10条数据）...")
    
    # 先获取历史数据
    historical_data = spider.fetch_single_stock_data(
        stock_code='600000',
        kline_type=KLineType.MIN_5,
        start_date='20250617',
        end_date='20250617'
    )
    
    if not historical_data.empty:
        # 模拟回放前10条数据
        sample_data = historical_data.head(10)
        for _, row in sample_data.iterrows():
            data_callback(row.to_dict())
            time.sleep(0.5)  # 每0.5秒播放一条数据

def advanced_manager_example():
    """高级管理器使用示例"""
    print("\n=== 高级管理器使用示例 ===")
    
    spider = StockDataSpider()
    manager = StockDataManager(spider)
    
    # 添加股票配置
    manager.add_stock('600000', '浦发银行', 'SH')
    manager.add_stock('000001', '平安银行', 'SZ')
    manager.add_stock('000002', '万科A', 'SZ')
    
    # 获取所有股票的日线数据
    print("获取所有配置股票的日线数据...")
    all_daily_data = manager.get_all_daily_data(
        start_date='20240601',
        end_date='20241231'
    )
    
    for code, df in all_daily_data.items():
        if not df.empty:
            print(f"股票 {code}: {len(df)} 条日线数据")
    
    # 获取分钟数据
    print("获取所有配置股票的分钟数据...")
    all_minute_data = manager.get_all_minute_data(days_back=3)
    
    for code, df in all_minute_data.items():
        if not df.empty:
            print(f"股票 {code}: {len(df)} 条分钟数据")

def incremental_update_example():
    """增量更新示例"""
    print("\n=== 增量更新示例 ===")
    
    spider = StockDataSpider()
    
    # 假设我们已有数据到某个时间点
    last_update_time = "20250617"
    
    print(f"获取从 {last_update_time} 以来的增量数据...")
    incremental_data = spider.fetch_incremental_data(
        stock_code='600000',
        kline_type=KLineType.DAILY,
        last_datetime=last_update_time
    )
    
    if not incremental_data.empty:
        print(f"获取到 {len(incremental_data)} 条增量数据")
        print("增量数据:")
        print(incremental_data[['datetime', 'open', 'high', 'low', 'close', 'volume']])

def market_status_example():
    """市场状态检查示例"""
    print("\n=== 市场状态检查示例 ===")
    
    spider = StockDataSpider()
    
    # 获取市场状态
    market_info = spider.get_market_status()
    
    print("当前市场状态:")
    for key, value in market_info.items():
        print(f"  {key}: {value}")
    
    if market_info['is_trading']:
        print("市场正在交易中，可以获取实时数据")
    else:
        print("市场休市中")

def data_analysis_example():
    """数据分析示例"""
    print("\n=== 数据分析示例 ===")
    
    spider = StockDataSpider()
    
    # 获取数据
    data = spider.fetch_single_stock_data(
        stock_code='600000',
        kline_type=KLineType.DAILY,
        start_date='20240101',
        end_date='20241231'
    )
    
    if not data.empty:
        print(f"数据统计分析 (股票: 600000):")
        print(f"  数据条数: {len(data)}")
        print(f"  时间范围: {data['datetime'].min()} 到 {data['datetime'].max()}")
        print(f"  最高价: {data['high'].max():.2f}")
        print(f"  最低价: {data['low'].min():.2f}")
        print(f"  平均收盘价: {data['close'].mean():.2f}")
        print(f"  总成交量: {data['volume'].sum():,}")
        
        # 计算涨跌幅
        data['price_change'] = data['close'].pct_change() * 100
        print(f"  最大单日涨幅: {data['price_change'].max():.2f}%")
        print(f"  最大单日跌幅: {data['price_change'].min():.2f}%")
        print(f"  平均日涨跌幅: {data['price_change'].mean():.2f}%")

if __name__ == "__main__":
    print("股票数据爬虫使用示例")
    print("=" * 50)
    
    try:
        # 运行各种示例
        basic_usage_example()
        multi_stock_example()
        rolling_minute_data_example()
        realtime_simulation_example()
        advanced_manager_example()
        incremental_update_example()
        market_status_example()
        data_analysis_example()
        
        print("\n" + "=" * 50)
        print("所有示例运行完成！")
        
    except KeyboardInterrupt:
        print("\n用户中断程序")
    except Exception as e:
        print(f"\n程序运行出错: {e}")
        import traceback
        traceback.print_exc()
