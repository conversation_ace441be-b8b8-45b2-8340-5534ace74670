# 股票数据爬虫 (Stock Data Spider)

基于东方财富接口的股票数据爬取模块，支持多线程并发爬取日线和分时数据。

## 功能特性

- ✅ **多股票并发爬取**: 支持同时爬取多只股票数据
- ✅ **多种K线类型**: 支持1分钟、5分钟、15分钟、30分钟、60分钟、日线、周线、月线
- ✅ **复权处理**: 支持不复权、前复权、后复权
- ✅ **时间区间设置**: 可指定开始和结束日期
- ✅ **滚动爬取**: 自动处理3000条数据限制，支持获取更长时间的分钟数据
- ✅ **实时回放**: 模拟历史数据的实时播放
- ✅ **异常处理**: 完善的网络超时和重试机制
- ✅ **数据缓存**: 内置数据缓存机制
- ✅ **CSV导出**: 支持将数据保存为CSV文件
- ✅ **增量更新**: 支持获取增量数据进行更新

## 安装依赖

```bash
pip install requests pandas
```

## 快速开始

### 基础使用

```python
from sprider import StockDataSpider, KLineType, AdjustType

# 创建爬虫实例
spider = StockDataSpider(max_workers=5, timeout=30, max_retries=3)

# 获取单只股票日线数据
daily_data = spider.fetch_single_stock_data(
    stock_code='600000',  # 浦发银行
    kline_type=KLineType.DAILY,
    adjust_type=AdjustType.FORWARD,
    start_date='********',
    end_date='********'
)

print(f"获取到 {len(daily_data)} 条日线数据")
print(daily_data.head())
```

### 多股票并发爬取

```python
# 定义股票列表
stocks = ['600000', '000001', '000002', '600036', '000858']

# 并发获取多只股票数据
results = spider.fetch_multiple_stocks_data(
    stock_codes=stocks,
    kline_type=KLineType.DAILY,
    start_date='********',
    end_date='********'
)

for code, df in results.items():
    if not df.empty:
        print(f"股票 {code}: {len(df)} 条数据")
```

### 获取分钟数据

```python
# 获取5分钟数据
minute_data = spider.fetch_single_stock_data(
    stock_code='600000',
    kline_type=KLineType.MIN_5,
    start_date='********',
    end_date='********'
)

# 滚动获取更长时间的分钟数据（解决3000条限制）
long_minute_data = spider.rolling_fetch_minute_data(
    stock_codes=['600000'],
    days_back=30,
    kline_type=KLineType.MIN_1
)
```

### 实时回放模拟

```python
def data_callback(data_point):
    print(f"[回放] {data_point['datetime']} - 收盘价: {data_point['close']:.2f}")

# 模拟实时回放
spider.simulate_realtime_playback(
    stock_code='600000',
    start_date='********',
    end_date='********',
    kline_type=KLineType.MIN_5,
    speed_multiplier=10.0,  # 10倍速播放
    callback=data_callback
)
```

### 高级管理器使用

```python
from sprider import StockDataManager

# 创建管理器
manager = StockDataManager(spider)

# 添加股票配置
manager.add_stock('600000', '浦发银行', 'SH')
manager.add_stock('000001', '平安银行', 'SZ')

# 获取所有股票数据
all_daily_data = manager.get_all_daily_data(start_date='********')
all_minute_data = manager.get_all_minute_data(days_back=7)

# 启动实时监控（示例）
# manager.start_realtime_monitor(interval_seconds=60)
```

## 支持的股票市场

| 市场 | secid前缀 | 股票代码示例 | 说明 |
|------|-----------|--------------|------|
| 上交所(沪市) | 1. | 600000 | 6开头的股票 |
| 深交所(深市) | 0. | 000001, 000002, 300xxx | 000、002、003、300、301开头 |
| 北交所 | 0. | 830964 | 8、4开头的股票 |

## K线类型说明

| 类型 | 值 | 说明 |
|------|----|----- |
| MIN_1 | 1 | 1分钟K线 |
| MIN_5 | 5 | 5分钟K线 |
| MIN_15 | 15 | 15分钟K线 |
| MIN_30 | 30 | 30分钟K线 |
| MIN_60 | 60 | 60分钟K线 |
| DAILY | 101 | 日K线 |
| WEEKLY | 102 | 周K线 |
| MONTHLY | 103 | 月K线 |

## 复权类型说明

| 类型 | 值 | 说明 |
|------|----|----- |
| NONE | 0 | 不复权 |
| FORWARD | 1 | 前复权 |
| BACKWARD | 2 | 后复权 |

## 数据字段说明

返回的DataFrame包含以下字段：

- `stock_code`: 股票代码
- `datetime`: 日期时间
- `open`: 开盘价
- `close`: 收盘价
- `high`: 最高价
- `low`: 最低价
- `volume`: 成交量
- `amount`: 成交额
- `amplitude`: 振幅
- `pct_change`: 涨跌幅(%)
- `change`: 涨跌额
- `turnover`: 换手率

## 配置参数

### StockDataSpider 参数

- `max_workers`: 最大线程数 (默认: 10)
- `timeout`: 请求超时时间，秒 (默认: 30)
- `max_retries`: 最大重试次数 (默认: 3)

### 请求参数

- `stock_code`: 股票代码 (如: '600000')
- `kline_type`: K线类型 (KLineType枚举)
- `adjust_type`: 复权类型 (AdjustType枚举)
- `start_date`: 开始日期 (格式: 'YYYYMMDD')
- `end_date`: 结束日期 (格式: 'YYYYMMDD')
- `limit`: 返回条数限制 (默认: 3000)

## 异常处理

爬虫内置了完善的异常处理机制：

- **网络超时**: 自动重试，支持指数退避
- **请求失败**: 多次重试机制
- **数据解析错误**: 跳过错误数据，继续处理
- **频率限制**: 自动添加随机延迟

## 注意事项

1. **请求频率**: 建议控制请求频率，避免被服务器限制
2. **数据量限制**: 单次请求最多返回3000条数据，使用滚动获取处理更长时间数据
3. **市场时间**: 注意股市交易时间，休市时无法获取实时数据
4. **网络环境**: 确保网络连接稳定，必要时可增加重试次数

## 快速测试

你可以直接运行以下命令测试爬虫功能：

```bash
# 测试获取单只股票数据
python cli.py fetch 600000 --kline daily --start ******** --end 20240131 --show

# 测试获取多只股票数据
python cli.py fetch-multi --list custom --kline daily --start ******** --end 20240131
```

## 许可证

本项目仅供学习和研究使用，请遵守相关法律法规和网站使用条款。

## 命令行工具

项目提供了便捷的命令行工具 `cli.py`：

### 查看帮助
```bash
python cli.py --help
```

### 列出可用股票列表
```bash
python cli.py list
```

### 获取单只股票数据
```bash
# 获取浦发银行日线数据
python cli.py fetch 600000 --kline daily --start ******** --end ******** --show

# 获取5分钟数据
python cli.py fetch 600000 --kline 5m --start ******** --end ********
```

### 获取多只股票数据
```bash
# 使用预设股票列表
python cli.py fetch-multi --list banks --kline daily --start ********

# 指定股票代码
python cli.py fetch-multi --codes 600000,000001,000002 --kline daily
```

### 获取分钟数据（滚动获取）
```bash
# 获取最近30天的1分钟数据
python cli.py fetch-minute --codes 600000 --kline 1m --days 30
```

### 实时监控
```bash
# 监控指定股票
python cli.py monitor --codes 600000,000001 --interval 60

# 监控预设股票列表
python cli.py monitor --list custom --interval 30
```

## 项目文件结构

```
.
├── sprider.py          # 核心爬虫模块
├── config.py           # 配置文件
├── cli.py              # 命令行工具
└── README.md           # 项目说明
```

## 更新日志

- v1.0.0: 初始版本，支持基础数据爬取功能
- 支持多线程并发、滚动获取、实时回放等高级功能
- 提供命令行工具和配置文件
- 完整的示例代码和文档
