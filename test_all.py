#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票数据爬虫完整功能测试
"""

import os
import time
from sprider import StockDataSpider, StockDataManager, KLineType, AdjustType
from config import get_stock_codes, STOCK_LISTS

def test_basic_functionality():
    """测试基础功能"""
    print("=" * 60)
    print("测试 1: 基础功能测试")
    print("=" * 60)
    
    spider = StockDataSpider(max_workers=3, timeout=30, max_retries=2)
    
    # 测试单只股票日线数据
    print("\n1.1 测试获取单只股票日线数据...")
    daily_data = spider.fetch_single_stock_data(
        stock_code='600000',
        kline_type=KLineType.DAILY,
        start_date='********',
        end_date='20240630'
    )
    
    assert not daily_data.empty, "日线数据不应为空"
    assert 'datetime' in daily_data.columns, "应包含datetime列"
    assert 'close' in daily_data.columns, "应包含close列"
    print(f"✓ 成功获取 {len(daily_data)} 条日线数据")
    
    # 测试分钟数据
    print("\n1.2 测试获取分钟数据...")
    minute_data = spider.fetch_single_stock_data(
        stock_code='600000',
        kline_type=KLineType.MIN_5,
        start_date='20250617',
        end_date='20250617'
    )
    
    if not minute_data.empty:
        print(f"✓ 成功获取 {len(minute_data)} 条5分钟数据")
    else:
        print("⚠ 分钟数据为空（可能是非交易时间）")
    
    print("✓ 基础功能测试通过")

def test_multi_stock_functionality():
    """测试多股票功能"""
    print("\n" + "=" * 60)
    print("测试 2: 多股票并发功能测试")
    print("=" * 60)
    
    spider = StockDataSpider(max_workers=3)
    
    # 测试多股票并发获取
    print("\n2.1 测试多股票并发获取...")
    stock_codes = ['600000', '000001']
    
    results = spider.fetch_multiple_stocks_data(
        stock_codes=stock_codes,
        kline_type=KLineType.DAILY,
        start_date='********',
        end_date='20240630'
    )
    
    assert len(results) == len(stock_codes), f"应返回 {len(stock_codes)} 只股票的数据"
    
    success_count = sum(1 for df in results.values() if not df.empty)
    print(f"✓ 成功获取 {success_count}/{len(stock_codes)} 只股票的数据")
    
    print("✓ 多股票并发功能测试通过")

def test_advanced_functionality():
    """测试高级功能"""
    print("\n" + "=" * 60)
    print("测试 3: 高级功能测试")
    print("=" * 60)
    
    spider = StockDataSpider()
    
    # 测试缓存功能
    print("\n3.1 测试缓存功能...")
    data1 = spider.fetch_single_stock_data('600000', KLineType.DAILY, start_date='********', end_date='********')
    cached_data = spider.get_cached_data('600000', KLineType.DAILY)
    
    if cached_data is not None and not cached_data.empty:
        print("✓ 缓存功能正常")
    else:
        print("⚠ 缓存功能异常")
    
    # 测试市场状态
    print("\n3.2 测试市场状态检查...")
    market_status = spider.get_market_status()
    assert 'current_time' in market_status, "应包含当前时间"
    assert 'market_status' in market_status, "应包含市场状态"
    print(f"✓ 当前市场状态: {market_status['market_status']}")
    
    # 测试增量更新
    print("\n3.3 测试增量更新...")
    incremental_data = spider.fetch_incremental_data(
        stock_code='600000',
        kline_type=KLineType.DAILY,
        last_datetime='20250617'
    )
    
    if not incremental_data.empty:
        print(f"✓ 成功获取 {len(incremental_data)} 条增量数据")
    else:
        print("⚠ 增量数据为空")
    
    print("✓ 高级功能测试通过")

def test_manager_functionality():
    """测试管理器功能"""
    print("\n" + "=" * 60)
    print("测试 4: 管理器功能测试")
    print("=" * 60)
    
    spider = StockDataSpider()
    manager = StockDataManager(spider)
    
    # 测试添加股票
    print("\n4.1 测试股票管理...")
    manager.add_stock('600000', '浦发银行', 'SH')
    manager.add_stock('000001', '平安银行', 'SZ')
    
    assert len(manager.stock_configs) == 2, "应有2只股票配置"
    print("✓ 股票添加成功")
    
    # 测试获取所有股票数据
    print("\n4.2 测试获取所有股票数据...")
    all_data = manager.get_all_daily_data(start_date='********', end_date='********')
    
    success_count = sum(1 for df in all_data.values() if not df.empty)
    print(f"✓ 成功获取 {success_count}/{len(manager.stock_configs)} 只股票的数据")
    
    print("✓ 管理器功能测试通过")

def test_config_functionality():
    """测试配置功能"""
    print("\n" + "=" * 60)
    print("测试 5: 配置功能测试")
    print("=" * 60)
    
    # 测试股票列表配置
    print("\n5.1 测试股票列表配置...")
    custom_codes = get_stock_codes('custom')
    banks_codes = get_stock_codes('banks')
    
    assert len(custom_codes) > 0, "自定义股票列表不应为空"
    assert len(banks_codes) > 0, "银行股票列表不应为空"
    print(f"✓ 自定义列表: {len(custom_codes)} 只股票")
    print(f"✓ 银行股列表: {len(banks_codes)} 只股票")
    
    # 测试所有预设列表
    print("\n5.2 测试所有预设股票列表...")
    for list_name in STOCK_LISTS.keys():
        codes = get_stock_codes(list_name)
        print(f"✓ {list_name}: {len(codes)} 只股票")
    
    print("✓ 配置功能测试通过")

def test_data_validation():
    """测试数据验证"""
    print("\n" + "=" * 60)
    print("测试 6: 数据验证测试")
    print("=" * 60)
    
    spider = StockDataSpider()
    
    # 获取测试数据
    print("\n6.1 获取测试数据...")
    data = spider.fetch_single_stock_data(
        stock_code='600000',
        kline_type=KLineType.DAILY,
        start_date='********',
        end_date='********'
    )
    
    if data.empty:
        print("⚠ 无法获取测试数据，跳过验证测试")
        return
    
    # 验证数据完整性
    print("\n6.2 验证数据完整性...")
    required_columns = ['datetime', 'open', 'high', 'low', 'close', 'volume']
    for col in required_columns:
        assert col in data.columns, f"缺少必需列: {col}"
    print("✓ 数据列完整")
    
    # 验证数据合理性
    print("\n6.3 验证数据合理性...")
    assert (data['high'] >= data['low']).all(), "最高价应大于等于最低价"
    assert (data['high'] >= data['open']).all(), "最高价应大于等于开盘价"
    assert (data['high'] >= data['close']).all(), "最高价应大于等于收盘价"
    assert (data['low'] <= data['open']).all(), "最低价应小于等于开盘价"
    assert (data['low'] <= data['close']).all(), "最低价应小于等于收盘价"
    assert (data['volume'] >= 0).all(), "成交量应大于等于0"
    print("✓ 数据逻辑正确")
    
    print("✓ 数据验证测试通过")

def test_error_handling():
    """测试错误处理"""
    print("\n" + "=" * 60)
    print("测试 7: 错误处理测试")
    print("=" * 60)
    
    spider = StockDataSpider(max_retries=1, timeout=5)
    
    # 测试无效股票代码
    print("\n7.1 测试无效股票代码...")
    invalid_data = spider.fetch_single_stock_data(
        stock_code='999999',  # 无效代码
        kline_type=KLineType.DAILY,
        start_date='********',
        end_date='********'
    )
    
    if invalid_data.empty:
        print("✓ 正确处理无效股票代码")
    else:
        print("⚠ 无效股票代码处理异常")
    
    # 测试无效日期范围
    print("\n7.2 测试无效日期范围...")
    invalid_date_data = spider.fetch_single_stock_data(
        stock_code='600000',
        kline_type=KLineType.DAILY,
        start_date='20300101',  # 未来日期
        end_date='20300131'
    )
    
    if invalid_date_data.empty:
        print("✓ 正确处理无效日期范围")
    else:
        print("⚠ 无效日期范围处理异常")
    
    print("✓ 错误处理测试通过")

def run_all_tests():
    """运行所有测试"""
    print("股票数据爬虫完整功能测试")
    print("=" * 60)
    print(f"开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        test_basic_functionality,
        test_multi_stock_functionality,
        test_advanced_functionality,
        test_manager_functionality,
        test_config_functionality,
        test_data_validation,
        test_error_handling,
    ]
    
    passed = 0
    failed = 0
    
    for test_func in tests:
        try:
            test_func()
            passed += 1
        except Exception as e:
            print(f"✗ 测试失败: {test_func.__name__}")
            print(f"  错误: {e}")
            failed += 1
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    print(f"总测试数: {len(tests)}")
    print(f"通过: {passed}")
    print(f"失败: {failed}")
    print(f"成功率: {passed/len(tests)*100:.1f}%")
    print(f"结束时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    if failed == 0:
        print("\n🎉 所有测试通过！爬虫功能正常！")
    else:
        print(f"\n⚠ 有 {failed} 个测试失败，请检查相关功能")

if __name__ == "__main__":
    run_all_tests()
